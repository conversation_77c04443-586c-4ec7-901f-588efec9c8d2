250605021540512390,pg_restore: connecting to database for restore

250605021540520097,pg_restore: creating SCHEMA "tiger"

250605021540522518,pg_restore: creating SCHEMA "tiger_data"

250605021540524458,pg_restore: creating SCHEMA "topology"

250605021540526357,pg_restore: creating COMMENT "SCHEMA topology"

250605021540528692,pg_restore: creating EXTENSION "fuzzystrmatch"

250605021540535037,pg_restore: creating COMMENT "EXTENSION fuzzystrmatch"

250605021540537404,pg_restore: creating EXTENSION "postgis"

250605021540817130,pg_restore: creating COMMENT "EXTENSION postgis"

250605021540819233,pg_restore: creating EXTENSION "postgis_raster"

250605021540976485,pg_restore: creating COMMENT "EXTENSION postgis_raster"

250605021540978928,pg_restore: creating EXTENSION "postgis_tiger_geocoder"

250605021541524784,pg_restore: creating COMMENT "EXTENSION postgis_tiger_geocoder"

250605021541527396,pg_restore: creating EXTENSION "postgis_topology"

250605021541567000,pg_restore: creating COMMENT "EXTENSION postgis_topology"

250605021541568892,pg_restore: creating TABLE "public.app_asset_deed"

250605021541576446,pg_restore: creating SEQUENCE "public.app_asset_deed_id_seq"

250605021541580153,pg_restore: creating SEQUENCE OWNED BY "public.app_asset_deed_id_seq"

250605021541581936,pg_restore: creating TABLE "public.app_asset_rent"

250605021541589205,pg_restore: creating SEQUENCE "public.app_asset_rent_id_seq"

250605021541592757,pg_restore: creating SEQUENCE OWNED BY "public.app_asset_rent_id_seq"

250605021541594990,pg_restore: creating TABLE "public.app_geom_building"

250605021541602307,pg_restore: creating SEQUENCE "public.app_geom_building_id_seq"

250605021541605809,pg_restore: creating SEQUENCE OWNED BY "public.app_geom_building_id_seq"

250605021541607885,pg_restore: creating TABLE "public.app_parcel_deed"

250605021541641184,pg_restore: creating SEQUENCE "public.app_parcel_deed_id_seq"

250605021541645044,pg_restore: creating SEQUENCE OWNED BY "public.app_parcel_deed_id_seq"

250605021541647950,pg_restore: creating TABLE "public.app_parcel_rent"

250605021541655923,pg_restore: creating SEQUENCE "public.app_parcel_rent_id_seq"

250605021541659993,pg_restore: creating SEQUENCE OWNED BY "public.app_parcel_rent_id_seq"

250605021541661688,pg_restore: creating TABLE "public.app_point_label"

250605021541669519,pg_restore: creating SEQUENCE "public.app_point_label_id_seq"

250605021541673600,pg_restore: creating SEQUENCE OWNED BY "public.app_point_label_id_seq"

250605021541675489,pg_restore: creating TABLE "public.app_point_rents"

250605021541683371,pg_restore: creating SEQUENCE "public.app_point_rents_id_seq"

250605021541687267,pg_restore: creating SEQUENCE OWNED BY "public.app_point_rents_id_seq"

250605021541689174,pg_restore: creating TABLE "public.app_point_texts"

250605021541696427,pg_restore: creating SEQUENCE "public.app_point_texts_id_seq"

250605021541699954,pg_restore: creating SEQUENCE OWNED BY "public.app_point_texts_id_seq"

250605021541701738,pg_restore: creating TABLE "public.app_profile"

250605021541706170,pg_restore: creating SEQUENCE "public.app_profile_id_seq"

250605021541710215,pg_restore: creating SEQUENCE OWNED BY "public.app_profile_id_seq"

250605021541711876,pg_restore: creating TABLE "public.app_side_plot_lines"

250605021541721167,pg_restore: creating SEQUENCE "public.app_side_plot_lines_id_seq"

250605021541725084,pg_restore: creating SEQUENCE OWNED BY "public.app_side_plot_lines_id_seq"

250605021541727157,pg_restore: creating TABLE "public.app_text_map"

250605021541735241,pg_restore: creating SEQUENCE "public.app_text_map_id_seq"

250605021541739052,pg_restore: creating SEQUENCE OWNED BY "public.app_text_map_id_seq"

250605021541741147,pg_restore: creating TABLE "public.app_time_line_asset_rent"

250605021541745100,pg_restore: creating SEQUENCE "public.app_time_line_asset_rent_id_seq"

250605021541749347,pg_restore: creating SEQUENCE OWNED BY "public.app_time_line_asset_rent_id_seq"

250605021541751388,pg_restore: creating TABLE "public.app_type_building"

250605021541758895,pg_restore: creating SEQUENCE "public.app_type_building_id_seq"

250605021541762450,pg_restore: creating SEQUENCE OWNED BY "public.app_type_building_id_seq"

250605021541764075,pg_restore: creating TABLE "public.app_type_user"

250605021541767929,pg_restore: creating SEQUENCE "public.app_type_user_id_seq"

250605021541771587,pg_restore: creating SEQUENCE OWNED BY "public.app_type_user_id_seq"

250605021541773386,pg_restore: creating TABLE "public.auth_group"

250605021541777762,pg_restore: creating SEQUENCE "public.auth_group_id_seq"

250605021541781821,pg_restore: creating SEQUENCE OWNED BY "public.auth_group_id_seq"

250605021541783886,pg_restore: creating TABLE "public.auth_group_permissions"

250605021541788480,pg_restore: creating SEQUENCE "public.auth_group_permissions_id_seq"

250605021541792940,pg_restore: creating SEQUENCE OWNED BY "public.auth_group_permissions_id_seq"

250605021541794982,pg_restore: creating TABLE "public.auth_permission"

250605021541798924,pg_restore: creating SEQUENCE "public.auth_permission_id_seq"

250605021541803110,pg_restore: creating SEQUENCE OWNED BY "public.auth_permission_id_seq"

250605021541805161,pg_restore: creating TABLE "public.auth_user"

250605021541814015,pg_restore: creating TABLE "public.auth_user_groups"

250605021541818966,pg_restore: creating SEQUENCE "public.auth_user_groups_id_seq"

250605021541823903,pg_restore: creating SEQUENCE OWNED BY "public.auth_user_groups_id_seq"

250605021541826738,pg_restore: creating SEQUENCE "public.auth_user_id_seq"

250605021541831694,pg_restore: creating SEQUENCE OWNED BY "public.auth_user_id_seq"

250605021541833946,pg_restore: creating TABLE "public.auth_user_user_permissions"

250605021541838283,pg_restore: creating SEQUENCE "public.auth_user_user_permissions_id_seq"

250605021541842918,pg_restore: creating SEQUENCE OWNED BY "public.auth_user_user_permissions_id_seq"

250605021541845221,pg_restore: creating TABLE "public.django_admin_log"

250605021541853206,pg_restore: creating SEQUENCE "public.django_admin_log_id_seq"

250605021541856760,pg_restore: creating SEQUENCE OWNED BY "public.django_admin_log_id_seq"

250605021541858745,pg_restore: creating TABLE "public.django_content_type"

250605021541862733,pg_restore: creating SEQUENCE "public.django_content_type_id_seq"

250605021541866903,pg_restore: creating SEQUENCE OWNED BY "public.django_content_type_id_seq"

250605021541868681,pg_restore: creating TABLE "public.django_migrations"

250605021541876499,pg_restore: creating SEQUENCE "public.django_migrations_id_seq"

250605021541880291,pg_restore: creating SEQUENCE OWNED BY "public.django_migrations_id_seq"

250605021541882344,pg_restore: creating TABLE "public.django_session"

250605021541890848,pg_restore: creating DEFAULT "public.app_asset_deed id"

250605021541892853,pg_restore: creating DEFAULT "public.app_asset_rent id"

250605021541894991,pg_restore: creating DEFAULT "public.app_geom_building id"

250605021541896790,pg_restore: creating DEFAULT "public.app_parcel_deed id"

250605021541898532,pg_restore: creating DEFAULT "public.app_parcel_rent id"

250605021541900711,pg_restore: creating DEFAULT "public.app_point_label id"

250605021541902480,pg_restore: creating DEFAULT "public.app_point_rents id"

250605021541905091,pg_restore: creating DEFAULT "public.app_point_texts id"

250605021541907649,pg_restore: creating DEFAULT "public.app_profile id"

250605021541908376,pg_restore: creating DEFAULT "public.app_side_plot_lines id"

250605021541909121,pg_restore: creating DEFAULT "public.app_text_map id"

250605021541910824,pg_restore: creating DEFAULT "public.app_time_line_asset_rent id"

250605021541912628,pg_restore: creating DEFAULT "public.app_type_building id"

250605021541914472,pg_restore: creating DEFAULT "public.app_type_user id"

250605021541916296,pg_restore: creating DEFAULT "public.auth_group id"

250605021541918201,pg_restore: creating DEFAULT "public.auth_group_permissions id"

250605021541920279,pg_restore: creating DEFAULT "public.auth_permission id"

250605021541922353,pg_restore: creating DEFAULT "public.auth_user id"

250605021541924440,pg_restore: creating DEFAULT "public.auth_user_groups id"

250605021541926353,pg_restore: creating DEFAULT "public.auth_user_user_permissions id"

250605021541928217,pg_restore: creating DEFAULT "public.django_admin_log id"

250605021541930220,pg_restore: creating DEFAULT "public.django_content_type id"

250605021541932984,pg_restore: creating DEFAULT "public.django_migrations id"

250605021541934329,pg_restore: processing data for table "public.app_asset_deed"

250605021541939119,pg_restore: processing data for table "public.app_asset_rent"

250605021541949283,pg_restore: processing data for table "public.app_geom_building"

250605021541955779,pg_restore: processing data for table "public.app_parcel_deed"

250605021541970403,pg_restore: processing data for table "public.app_parcel_rent"

250605021542018205,pg_restore: processing data for table "public.app_point_label"

250605021542024217,pg_restore: processing data for table "public.app_point_rents"

250605021542027275,pg_restore: processing data for table "public.app_point_texts"

250605021542030606,pg_restore: processing data for table "public.app_profile"

250605021542032891,pg_restore: processing data for table "public.app_side_plot_lines"

250605021542036431,pg_restore: processing data for table "public.app_text_map"

250605021542039179,pg_restore: processing data for table "public.app_time_line_asset_rent"

250605021542047113,pg_restore: processing data for table "public.app_type_building"

250605021542050308,pg_restore: processing data for table "public.app_type_user"

250605021542053267,pg_restore: processing data for table "public.auth_group"

250605021542055444,pg_restore: processing data for table "public.auth_group_permissions"

250605021542057738,pg_restore: processing data for table "public.auth_permission"

250605021542058413,pg_restore: processing data for table "public.auth_user"

250605021542059479,pg_restore: processing data for table "public.auth_user_groups"

250605021542060823,pg_restore: processing data for table "public.auth_user_user_permissions"

250605021542062711,pg_restore: processing data for table "public.django_admin_log"

250605021542065712,pg_restore: processing data for table "public.django_content_type"

250605021542068374,pg_restore: processing data for table "public.django_migrations"

250605021542071212,pg_restore: processing data for table "public.django_session"

250605021542077686,pg_restore: processing data for table "public.spatial_ref_sys"

250605021542079112,pg_restore: processing data for table "tiger.geocode_settings"

250605021542080639,pg_restore: processing data for table "tiger.pagc_gaz"

250605021542082309,pg_restore: processing data for table "tiger.pagc_lex"

250605021542083307,pg_restore: processing data for table "tiger.pagc_rules"

250605021542084469,pg_restore: processing data for table "topology.topology"

250605021542085161,pg_restore: processing data for table "topology.layer"

250605021542086002,pg_restore: executing SEQUENCE SET app_asset_deed_id_seq

250605021542086724,pg_restore: executing SEQUENCE SET app_asset_rent_id_seq

250605021542087530,pg_restore: executing SEQUENCE SET app_geom_building_id_seq

250605021542089046,pg_restore: executing SEQUENCE SET app_parcel_deed_id_seq

250605021542090774,pg_restore: executing SEQUENCE SET app_parcel_rent_id_seq

250605021542092744,pg_restore: executing SEQUENCE SET app_point_label_id_seq

250605021542094774,pg_restore: executing SEQUENCE SET app_point_rents_id_seq

250605021542096874,pg_restore: executing SEQUENCE SET app_point_texts_id_seq

250605021542098997,pg_restore: executing SEQUENCE SET app_profile_id_seq

250605021542100868,pg_restore: executing SEQUENCE SET app_side_plot_lines_id_seq

250605021542102763,pg_restore: executing SEQUENCE SET app_text_map_id_seq

250605021542105315,pg_restore: executing SEQUENCE SET app_time_line_asset_rent_id_seq

250605021542106664,pg_restore: executing SEQUENCE SET app_type_building_id_seq

250605021542108652,pg_restore: executing SEQUENCE SET app_type_user_id_seq

250605021542110670,pg_restore: executing SEQUENCE SET auth_group_id_seq

250605021542112475,pg_restore: executing SEQUENCE SET auth_group_permissions_id_seq

250605021542114442,pg_restore: executing SEQUENCE SET auth_permission_id_seq

250605021542116311,pg_restore: executing SEQUENCE SET auth_user_groups_id_seq

250605021542118139,pg_restore: executing SEQUENCE SET auth_user_id_seq

250605021542120170,pg_restore: executing SEQUENCE SET auth_user_user_permissions_id_seq

250605021542121997,pg_restore: executing SEQUENCE SET django_admin_log_id_seq

250605021542123809,pg_restore: executing SEQUENCE SET django_content_type_id_seq

250605021542125673,pg_restore: executing SEQUENCE SET django_migrations_id_seq

250605021542130012,pg_restore: executing SEQUENCE SET topology_id_seq

250605021542130748,pg_restore: creating CONSTRAINT "public.app_asset_deed app_asset_deed_pkey"

250605021542136197,pg_restore: creating CONSTRAINT "public.app_asset_rent app_asset_rent_pkey"

250605021542143064,pg_restore: creating CONSTRAINT "public.app_geom_building app_geom_building_pkey"

250605021542149246,pg_restore: creating CONSTRAINT "public.app_parcel_deed app_parcel_deed_pkey"

250605021542155067,pg_restore: creating CONSTRAINT "public.app_parcel_rent app_parcel_rent_pkey"

250605021542162803,pg_restore: creating CONSTRAINT "public.app_point_label app_point_label_pkey"

250605021542169176,pg_restore: creating CONSTRAINT "public.app_point_rents app_point_rents_pkey"

250605021542175624,pg_restore: creating CONSTRAINT "public.app_point_texts app_point_texts_pkey"

250605021542181938,pg_restore: creating CONSTRAINT "public.app_point_texts app_point_texts_point_rents_id_key"

250605021542189160,pg_restore: creating CONSTRAINT "public.app_profile app_profile_pkey"

250605021542195872,pg_restore: creating CONSTRAINT "public.app_profile app_profile_user_id_key"

250605021542203278,pg_restore: creating CONSTRAINT "public.app_side_plot_lines app_side_plot_lines_pkey"

250605021542210745,pg_restore: creating CONSTRAINT "public.app_text_map app_text_map_pkey"

250605021542218957,pg_restore: creating CONSTRAINT "public.app_time_line_asset_rent app_time_line_asset_rent_pkey"

250605021542226604,pg_restore: creating CONSTRAINT "public.app_type_building app_type_building_pkey"

250605021542232878,pg_restore: creating CONSTRAINT "public.app_type_user app_type_user_pkey"

250605021542239430,pg_restore: creating CONSTRAINT "public.auth_group auth_group_name_key"

250605021542246391,pg_restore: creating CONSTRAINT "public.auth_group_permissions auth_group_permissions_group_id_permission_id_0cd325b0_uniq"

250605021542253597,pg_restore: creating CONSTRAINT "public.auth_group_permissions auth_group_permissions_pkey"

250605021542261168,pg_restore: creating CONSTRAINT "public.auth_group auth_group_pkey"

250605021542268593,pg_restore: creating CONSTRAINT "public.auth_permission auth_permission_content_type_id_codename_01ab375a_uniq"

250605021542275414,pg_restore: creating CONSTRAINT "public.auth_permission auth_permission_pkey"

250605021542283003,pg_restore: creating CONSTRAINT "public.auth_user_groups auth_user_groups_pkey"

250605021542288817,pg_restore: creating CONSTRAINT "public.auth_user_groups auth_user_groups_user_id_group_id_94350c0c_uniq"

250605021542294658,pg_restore: creating CONSTRAINT "public.auth_user auth_user_pkey"

250605021542302436,pg_restore: creating CONSTRAINT "public.auth_user_user_permissions auth_user_user_permissions_pkey"

250605021542308495,pg_restore: creating CONSTRAINT "public.auth_user_user_permissions auth_user_user_permissions_user_id_permission_id_14a6b632_uniq"

250605021542314733,pg_restore: creating CONSTRAINT "public.auth_user auth_user_username_key"

250605021542321079,pg_restore: creating CONSTRAINT "public.django_admin_log django_admin_log_pkey"

250605021542327509,pg_restore: creating CONSTRAINT "public.django_content_type django_content_type_app_label_model_76bd3d3b_uniq"

250605021542333947,pg_restore: creating CONSTRAINT "public.django_content_type django_content_type_pkey"

250605021542339457,pg_restore: creating CONSTRAINT "public.django_migrations django_migrations_pkey"

250605021542345236,pg_restore: creating CONSTRAINT "public.django_session django_session_pkey"

250605021542355039,pg_restore: creating INDEX "public.app_asset_deed_parcel_deed_id_fd5b1ce6"

250605021542362066,pg_restore: creating INDEX "public.app_geom_building_geom_id"

250605021542366608,pg_restore: creating INDEX "public.app_geom_building_type_building_id_21cdaab4"

250605021542372683,pg_restore: creating INDEX "public.app_parcel_deed_geom_id"

250605021542376748,pg_restore: creating INDEX "public.app_parcel_rent_geom_id"

250605021542383901,pg_restore: creating INDEX "public.app_point_label_geom_id"

250605021542386543,pg_restore: creating INDEX "public.app_point_rents_geom_id"

250605021542388542,pg_restore: creating INDEX "public.app_point_rents_time_line_asset_rent_id_9aa80dc4"

250605021542394563,pg_restore: creating INDEX "public.app_point_texts_geom_id"

250605021542396746,pg_restore: creating INDEX "public.app_profile_type_user_id_c6187982"

250605021542401948,pg_restore: creating INDEX "public.app_side_plot_lines_geom_id"

250605021542404899,pg_restore: creating INDEX "public.app_side_plot_lines_time_line_asset_rent_id_9d5974e1"

250605021542409820,pg_restore: creating INDEX "public.app_text_map_geom_id"

250605021542412222,pg_restore: creating INDEX "public.app_text_map_time_line_asset_rent_id_bf6229be"

250605021542418394,pg_restore: creating INDEX "public.app_time_line_asset_rent_asset_rent_id_46b907d6"

250605021542425308,pg_restore: creating INDEX "public.app_time_line_asset_rent_parcel_rent_id_ec438e4c"

250605021542432039,pg_restore: creating INDEX "public.auth_group_name_a6ea08ec_like"

250605021542438238,pg_restore: creating INDEX "public.auth_group_permissions_group_id_b120cbf9"

250605021542443660,pg_restore: creating INDEX "public.auth_group_permissions_permission_id_84c5c92e"

250605021542448866,pg_restore: creating INDEX "public.auth_permission_content_type_id_2f476e4b"

250605021542454465,pg_restore: creating INDEX "public.auth_user_groups_group_id_97559544"

250605021542460963,pg_restore: creating INDEX "public.auth_user_groups_user_id_6a12ed8b"

250605021542467461,pg_restore: creating INDEX "public.auth_user_user_permissions_permission_id_1fbb5f2c"

250605021542473788,pg_restore: creating INDEX "public.auth_user_user_permissions_user_id_a95ead1b"

250605021542479483,pg_restore: creating INDEX "public.auth_user_username_6821ab7c_like"

250605021542485455,pg_restore: creating INDEX "public.django_admin_log_content_type_id_c4bce8eb"

250605021542491782,pg_restore: creating INDEX "public.django_admin_log_user_id_c564eba6"

250605021542498049,pg_restore: creating INDEX "public.django_session_expire_date_a5c62663"

250605021542504362,pg_restore: creating INDEX "public.django_session_session_key_c0390e0f_like"

250605021542510759,pg_restore: creating FK CONSTRAINT "public.app_asset_deed app_asset_deed_parcel_deed_id_fd5b1ce6_fk_app_parcel_deed_id"

250605021542514788,pg_restore: creating FK CONSTRAINT "public.app_geom_building app_geom_building_type_building_id_21cdaab4_fk_app_type_"

250605021542516474,pg_restore: creating FK CONSTRAINT "public.app_point_rents app_point_rents_time_line_asset_rent_9aa80dc4_fk_app_time_"

250605021542520036,pg_restore: creating FK CONSTRAINT "public.app_point_texts app_point_texts_point_rents_id_dd290406_fk_app_point_rents_id"

250605021542521645,pg_restore: creating FK CONSTRAINT "public.app_profile app_profile_type_user_id_c6187982_fk_app_type_user_id"

250605021542523316,pg_restore: creating FK CONSTRAINT "public.app_profile app_profile_user_id_87d292a0_fk_auth_user_id"

250605021542526070,pg_restore: creating FK CONSTRAINT "public.app_side_plot_lines app_side_plot_lines_time_line_asset_rent_9d5974e1_fk_app_time_"

250605021542528735,pg_restore: creating FK CONSTRAINT "public.app_text_map app_text_map_time_line_asset_rent_bf6229be_fk_app_time_"

250605021542531105,pg_restore: creating FK CONSTRAINT "public.app_time_line_asset_rent app_time_line_asset__asset_rent_id_46b907d6_fk_app_asset"

250605021542533584,pg_restore: creating FK CONSTRAINT "public.app_time_line_asset_rent app_time_line_asset__parcel_rent_id_ec438e4c_fk_app_parce"

250605021542536559,pg_restore: creating FK CONSTRAINT "public.auth_group_permissions auth_group_permissio_permission_id_84c5c92e_fk_auth_perm"

250605021542538837,pg_restore: creating FK CONSTRAINT "public.auth_group_permissions auth_group_permissions_group_id_b120cbf9_fk_auth_group_id"

250605021542540995,pg_restore: creating FK CONSTRAINT "public.auth_permission auth_permission_content_type_id_2f476e4b_fk_django_co"

250605021542543203,pg_restore: creating FK CONSTRAINT "public.auth_user_groups auth_user_groups_group_id_97559544_fk_auth_group_id"

250605021542545181,pg_restore: creating FK CONSTRAINT "public.auth_user_groups auth_user_groups_user_id_6a12ed8b_fk_auth_user_id"

250605021542547316,pg_restore: creating FK CONSTRAINT "public.auth_user_user_permissions auth_user_user_permi_permission_id_1fbb5f2c_fk_auth_perm"

250605021542549807,pg_restore: creating FK CONSTRAINT "public.auth_user_user_permissions auth_user_user_permissions_user_id_a95ead1b_fk_auth_user_id"

250605021542552097,pg_restore: creating FK CONSTRAINT "public.django_admin_log django_admin_log_content_type_id_c4bce8eb_fk_django_co"

250605021542554266,pg_restore: creating FK CONSTRAINT "public.django_admin_log django_admin_log_user_id_c564eba6_fk_auth_user_id"

250605021542556367,pg_restore: creating ACL "SCHEMA topology"

250605021542558192,pg_restore: creating ACL "topology.TABLE layer"

250605021542559968,pg_restore: creating ACL "topology.TABLE topology"

250605021542561719,pg_restore: creating ACL "public.TABLE spatial_ref_sys"

250605021542563547,pg_restore: creating ACL "topology.SEQUENCE topology_id_seq"

